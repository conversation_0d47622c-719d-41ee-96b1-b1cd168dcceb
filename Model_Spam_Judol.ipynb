#Install Libraries (Jika di Google Colab)
# !pip install transformers datasets scikit-learn

import pandas as pd
import seaborn as sns
from sklearn.model_selection import train_test_split
from datasets import Dataset
from transformers import BertTokenizer, BertForSequenceClassification, Trainer, TrainingArguments
import torch

#Load & Preprocess Data
# Misal file CSV dengan kolom 'komentar' dan 'label' (0=tidak judol, 1=judol)
df = pd.read_csv('Dataset.csv')


# Menampilkan 5 baris pertama dataset
print("=== 5 Baris Pertama Dataset ===")
df.head()

# Informasi umum tentang dataset
print("=== Informasi Dataset ===")
print(f"Jumlah baris: {df.shape[0]}")
print(f"Jumlah kolom: {df.shape[1]}")
print(f"\nNama kolom: {list(df.columns)}")
print(f"\nTipe data:")
print(df.dtypes)
print(f"\nInformasi missing values:")
print(df.isnull().sum())

# Distribusi label dalam bentuk angka
print("=== Distribusi Label ===")
label_counts = df['Label'].value_counts()
print(label_counts)
print(f"\nPersentase:")
print(df['Label'].value_counts(normalize=True) * 100)

# Analisis panjang teks
print("=== Analisis Panjang Teks ===")

# Pastikan kolom Comment ada
if 'Comment' in df.columns:
    # Buat kolom sementara untuk analisis
    df['text_length'] = df['Comment'].str.len()
    df['word_count'] = df['Comment'].str.split().str.len()
    
    print(f"Statistik panjang karakter:")
    print(df['text_length'].describe())
    print(f"\nStatistik jumlah kata:")
    print(df['word_count'].describe())
else:
    print("❌ Error: Kolom 'Comment' tidak ditemukan!")
    print(f"Kolom yang tersedia: {list(df.columns)}")

# Perbandingan panjang teks berdasarkan label
print("=== Perbandingan Panjang Teks Berdasarkan Label ===")

# Pastikan kolom yang diperlukan ada
if 'text_length' in df.columns and 'word_count' in df.columns:
    print("\nPanjang karakter berdasarkan label:")
    print(df.groupby('Label')['text_length'].describe())
    print("\nJumlah kata berdasarkan label:")
    print(df.groupby('Label')['word_count'].describe())
else:
    print("❌ Error: Kolom 'text_length' atau 'word_count' tidak ditemukan!")
    print("Pastikan cell sebelumnya (Analisis Panjang Teks) sudah dijalankan.")
    print(f"Kolom yang tersedia: {list(df.columns)}")

print(df.columns)

# PENTING: Membersihkan kolom sementara yang dibuat untuk analisis
# Kolom ini hanya diperlukan untuk analisis data understanding
# dan akan dihapus sebelum preprocessing untuk menghindari konflik

print("=== Pembersihan Kolom Sementara ===")
print(f"Kolom sebelum pembersihan: {df.columns.tolist()}")

if 'text_length' in df.columns:
    df = df.drop(['text_length', 'word_count'], axis=1)
    print("✅ Kolom sementara berhasil dihapus: text_length, word_count")
else:
    print("ℹ️  Kolom sementara tidak ditemukan (sudah dihapus atau belum dibuat)")
    
print(f"Kolom setelah pembersihan: {df.columns.tolist()}")
print(f"\n⚠️  CATATAN: Jika Anda mendapat error 'KeyError: text_length',")
print(f"   pastikan untuk menjalankan cell ini setelah menjalankan")
print(f"   semua cell di bagian Data Understanding.")

#Pastikan semua nama kolom lower case agar konsisten
df.columns = df.columns.str.lower()
print("Nama kolom setelah normalisasi:", df.columns.tolist())

#Cek nama kolom label (asumsi: 'label' dalam huruf kecil)
if 'label' not in df.columns:
    print("Nama kolom label:", df.columns)  # tampilkan semua nama kolom agar bisa dicek
    raise ValueError("Kolom 'label' tidak ditemukan!")

#Buang baris dengan label kosong (NaN)
initial_count = len(df)
df = df.dropna(subset=['label'])
after_count = len(df)
print(f"Data sebelum: {initial_count}, setelah buang label kosong: {after_count}")

#Split data (stratify harus label tanpa NaN)
train_df, val_df = train_test_split(
    df,
    test_size=0.2,
    stratify=df['label'],
    random_state=42
)

print("=== Informasi Data Splitting ===")
print(f"Jumlah train data: {len(train_df)}")
print(f"Jumlah validasi data: {len(val_df)}")
print(f"\nDistribusi label di training set:")
print(train_df['label'].value_counts(normalize=True) * 100)
print(f"\nDistribusi label di validation set:")
print(val_df['label'].value_counts(normalize=True) * 100)

# Menampilkan contoh komentar spam (label = 1)
print("=== Contoh Komentar SPAM (Label = 1) ===")
spam_samples = df[df['Label'] == 1.0]['Comment'].head(10)
for i, comment in enumerate(spam_samples, 1):
    print(f"{i}. {comment}")
    print()

# Menampilkan contoh komentar non-spam (label = 0)
print("=== Contoh Komentar NON-SPAM (Label = 0) ===")
non_spam_samples = df[df['Label'] == 0.0]['Comment'].head(10)
for i, comment in enumerate(non_spam_samples, 1):
    print(f"{i}. {comment}")
    print()

import matplotlib.pyplot as plt
import seaborn as sns

# Set style untuk visualisasi
plt.style.use('default')
sns.set_palette("husl")

# Membuat subplot untuk visualisasi
fig, axes = plt.subplots(2, 2, figsize=(15, 12))

# 1. Bar plot distribusi label
sns.countplot(data=df, x='Label', ax=axes[0,0])
axes[0,0].set_title('Distribusi Label (Bar Plot)')
axes[0,0].set_xlabel('Label (0=Non-Spam, 1=Spam)')
axes[0,0].set_ylabel('Jumlah')

# 2. Pie chart distribusi label
label_counts = df['Label'].value_counts()
axes[0,1].pie(label_counts.values, labels=['Spam', 'Non-Spam'], autopct='%1.1f%%', startangle=90)
axes[0,1].set_title('Distribusi Label (Pie Chart)')

# 3. Histogram panjang teks (jika kolom tersedia)
if 'text_length' in df.columns:
    axes[1,0].hist(df[df['Label']==0]['text_length'], alpha=0.7, label='Non-Spam', bins=30)
    axes[1,0].hist(df[df['Label']==1]['text_length'], alpha=0.7, label='Spam', bins=30)
    axes[1,0].set_title('Distribusi Panjang Teks')
    axes[1,0].set_xlabel('Panjang Karakter')
    axes[1,0].set_ylabel('Frekuensi')
    axes[1,0].legend()
else:
    axes[1,0].text(0.5, 0.5, 'Kolom text_length\ntidak tersedia\n\nJalankan cell analisis\npanjang teks terlebih dahulu', 
                   ha='center', va='center', transform=axes[1,0].transAxes, fontsize=10)
    axes[1,0].set_title('Distribusi Panjang Teks (Data Tidak Tersedia)')

# 4. Box plot panjang teks berdasarkan label (jika kolom tersedia)
if 'text_length' in df.columns:
    sns.boxplot(data=df, x='Label', y='text_length', ax=axes[1,1])
    axes[1,1].set_title('Box Plot Panjang Teks per Label')
    axes[1,1].set_xlabel('Label (0=Non-Spam, 1=Spam)')
    axes[1,1].set_ylabel('Panjang Karakter')
else:
    axes[1,1].text(0.5, 0.5, 'Kolom text_length\ntidak tersedia\n\nJalankan cell analisis\npanjang teks terlebih dahulu', 
                   ha='center', va='center', transform=axes[1,1].transAxes, fontsize=10)
    axes[1,1].set_title('Box Plot Panjang Teks (Data Tidak Tersedia)')

plt.tight_layout()
plt.show()

from collections import Counter
import re

# Fungsi untuk membersihkan teks
def clean_text(text):
    # Mengubah ke lowercase dan menghapus karakter khusus
    text = re.sub(r'[^a-zA-Z\s]', '', text.lower())
    return text

# Menganalisis kata-kata paling umum untuk spam
spam_texts = df[df['Label'] == 1.0]['Comment'].apply(clean_text)
spam_words = ' '.join(spam_texts).split()
spam_word_freq = Counter(spam_words)

print("=== 20 Kata Paling Umum dalam SPAM ===")
for word, freq in spam_word_freq.most_common(20):
    print(f"{word}: {freq}")

# Menganalisis kata-kata paling umum untuk non-spam
non_spam_texts = df[df['Label'] == 0.0]['Comment'].apply(clean_text)
non_spam_words = ' '.join(non_spam_texts).split()
non_spam_word_freq = Counter(non_spam_words)

print("=== 20 Kata Paling Umum dalam NON-SPAM ===")
for word, freq in non_spam_word_freq.most_common(20):
    print(f"{word}: {freq}")

# Mengecek duplikasi data
print("=== Analisis Duplikasi ===")
print(f"Jumlah data duplikat: {df.duplicated().sum()}")
print(f"Jumlah komentar unik: {df['Comment'].nunique()}")
print(f"Total data: {len(df)}")

# Mengecek data kosong atau sangat pendek
print("\n=== Analisis Data Kosong/Pendek ===")
empty_comments = df[df['Comment'].str.len() < 5]
print(f"Jumlah komentar dengan panjang < 5 karakter: {len(empty_comments)}")

if len(empty_comments) > 0:
    print("Contoh komentar pendek:")
    for comment in empty_comments['Comment'].head():
        print(f"'{comment}'")

# Ringkasan temuan dari data understanding
print("=== RINGKASAN DATA UNDERSTANDING ===")
print(f"\n1. INFORMASI DATASET:")
print(f"   - Total data: {len(df)} komentar")
print(f"   - Jumlah fitur: {df.shape[1]}")
print(f"   - Missing values: {df.isnull().sum().sum()}")

print(f"\n2. DISTRIBUSI LABEL:")
label_dist = df['Label'].value_counts(normalize=True) * 100
print(f"   - Spam (1): {label_dist[1.0]:.1f}%")
print(f"   - Non-Spam (0): {label_dist[0.0]:.1f}%")
print(f"   - Dataset relatif seimbang: {'Ya' if abs(label_dist[1.0] - label_dist[0.0]) < 10 else 'Tidak'}")

print(f"\n3. KARAKTERISTIK TEKS:")
if 'text_length' in df.columns and 'word_count' in df.columns:
    print(f"   - Rata-rata panjang karakter: {df['text_length'].mean():.1f}")
    print(f"   - Rata-rata jumlah kata: {df['word_count'].mean():.1f}")
    print(f"   - Panjang teks spam vs non-spam: {'Berbeda signifikan' if abs(df[df['Label']==1]['text_length'].mean() - df[df['Label']==0]['text_length'].mean()) > 10 else 'Relatif sama'}")
else:
    print(f"   - Analisis karakteristik teks belum dilakukan")
    print(f"   - Jalankan cell 'Analisis Panjang Teks' terlebih dahulu")

print(f"\n4. KUALITAS DATA:")
print(f"   - Data duplikat: {df.duplicated().sum()}")
print(f"   - Komentar sangat pendek (<5 karakter): {len(df[df['Comment'].str.len() < 5])}")
print(f"   - Kualitas data: {'Baik' if df.duplicated().sum() < len(df)*0.05 and len(df[df['Comment'].str.len() < 5]) < len(df)*0.01 else 'Perlu pembersihan'}")

print(f"\n5. REKOMENDASI PREPROCESSING:")
print(f"   - Normalisasi teks (lowercase, remove special chars)")
print(f"   - Tokenisasi dan stemming/lemmatization")
print(f"   - Penanganan kata-kata umum (stopwords)")
print(f"   - Feature engineering (TF-IDF, word embeddings)")
if df.duplicated().sum() > 0:
    print(f"   - Penghapusan data duplikat")
if len(df[df['Comment'].str.len() < 5]) > 0:
    print(f"   - Penanganan komentar sangat pendek")

df['label'].value_counts().plot(kind="pie",autopct="%.1f%%")
plt.title("Label")
plt.show()

# EMERGENCY RESET - Jalankan jika terjadi error KeyError
# Ini akan memuat ulang data dari awal

print("=== EMERGENCY RESET ===")
try:
    # Reload data dari file CSV
    df = pd.read_csv('/content/Dataset.csv')
    print("✅ Data berhasil dimuat ulang")
    
    # Normalisasi nama kolom
    df.columns = df.columns.str.lower()
    print("✅ Nama kolom berhasil dinormalisasi")
    
    # Tampilkan info dataset
    print(f"\nInfo dataset setelah reset:")
    print(f"- Shape: {df.shape}")
    print(f"- Columns: {list(df.columns)}")
    print(f"- Missing values: {df.isnull().sum().sum()}")
    
    print("\n🎯 Sekarang Anda bisa menjalankan cell analisis dari awal")
    
except Exception as e:
    print(f"❌ Error saat reset: {e}")
    print("Pastikan file Dataset.csv tersedia di path yang benar")

#Load Tokenizer IndoBERT
print("=== Setup Tokenizer IndoBERT ===")
tokenizer = BertTokenizer.from_pretrained('indobenchmark/indobert-base-p1')
print("✅ IndoBERT tokenizer berhasil dimuat!")
print(f"   - Vocabulary size: {tokenizer.vocab_size}")
print(f"   - Model max length: {tokenizer.model_max_length}")

#Load Model IndoBERT for Classification
model = BertForSequenceClassification.from_pretrained(
    'indobenchmark/indobert-base-p1',
    num_labels=2  # binary classification
)

#Training Arguments & Metric
from transformers import TrainingArguments
training_args = TrainingArguments(
    output_dir='./results',
    eval_strategy='epoch',
    save_strategy='epoch',
    learning_rate=2e-5,
    per_device_train_batch_size=16,
    per_device_eval_batch_size=32,
    num_train_epochs=3,
    weight_decay=0.01,
    logging_dir='./logs',
    logging_steps=20,
    save_total_limit=1,
    load_best_model_at_end=True,
    metric_for_best_model='accuracy'
)


from sklearn.metrics import accuracy_score, f1_score

def compute_metrics(eval_pred):
    logits, labels = eval_pred
    preds = logits.argmax(axis=-1)
    acc = accuracy_score(labels, preds)
    f1 = f1_score(labels, preds)
    return {'accuracy': acc, 'f1': f1}

# Setup Early Stopping untuk mencegah overfitting
from transformers import EarlyStoppingCallback

# Early stopping callback
early_stopping = EarlyStoppingCallback(
    early_stopping_patience=3,  # Stop jika tidak ada improvement dalam 3 evaluasi
    early_stopping_threshold=0.001  # Minimum improvement threshold
)

print("Early stopping callback telah disiapkan")
print("- Patience: 3 evaluasi")
print("- Threshold: 0.001")

# Custom callback untuk monitoring overfitting
from transformers import TrainerCallback
import matplotlib.pyplot as plt

class OverfittingMonitor(TrainerCallback):
    def __init__(self):
        self.train_losses = []
        self.eval_losses = []
        self.eval_accuracies = []
        self.steps = []

    def on_log(self, args, state, control, model=None, logs=None, **kwargs):
        if 'train_loss' in logs:
            self.train_losses.append(logs['train_loss'])
            self.steps.append(state.global_step)

    def on_evaluate(self, args, state, control, model=None, logs=None, **kwargs):
        if logs:
            if 'eval_loss' in logs:
                self.eval_losses.append(logs['eval_loss'])
            if 'eval_accuracy' in logs:
                self.eval_accuracies.append(logs['eval_accuracy'])

            # Cek overfitting
            if len(self.eval_losses) >= 2:
                current_eval_loss = self.eval_losses[-1]
                previous_eval_loss = self.eval_losses[-2]

                if current_eval_loss > previous_eval_loss:
                    print(f"⚠️  WARNING: Validation loss meningkat dari {previous_eval_loss:.4f} ke {current_eval_loss:.4f}")
                    print("   Kemungkinan mulai overfitting!")
                else:
                    print(f"✅ Validation loss turun: {previous_eval_loss:.4f} → {current_eval_loss:.4f}")

    def plot_training_progress(self):
        if len(self.eval_losses) > 0:
            plt.figure(figsize=(12, 4))

            # Plot losses
            plt.subplot(1, 2, 1)
            if len(self.train_losses) > 0:
                plt.plot(self.steps, self.train_losses, label='Training Loss', color='blue')
            if len(self.eval_losses) > 0:
                eval_steps = self.steps[::len(self.steps)//len(self.eval_losses)][:len(self.eval_losses)]
                plt.plot(eval_steps, self.eval_losses, label='Validation Loss', color='red', marker='o')
            plt.xlabel('Steps')
            plt.ylabel('Loss')
            plt.title('Training vs Validation Loss')
            plt.legend()
            plt.grid(True)

            # Plot accuracy
            plt.subplot(1, 2, 2)
            if len(self.eval_accuracies) > 0:
                eval_steps = self.steps[::len(self.steps)//len(self.eval_accuracies)][:len(self.eval_accuracies)]
                plt.plot(eval_steps, self.eval_accuracies, label='Validation Accuracy', color='green', marker='s')
            plt.xlabel('Steps')
            plt.ylabel('Accuracy')
            plt.title('Validation Accuracy')
            plt.legend()
            plt.grid(True)

            plt.tight_layout()
            plt.show()

# Inisialisasi monitor
overfitting_monitor = OverfittingMonitor()
print("Overfitting monitor telah disiapkan")

# Fix the dataset preparation
# Ensure labels are integers
df['label'] = df['label'].astype(int)

# Recreate train/val split with proper labels
train_df, val_df = train_test_split(df, test_size=0.2, stratify=df['label'], random_state=42)

# Rename label column to 'labels' for HuggingFace compatibility
train_df = train_df.rename(columns={'label': 'labels'})
val_df = val_df.rename(columns={'label': 'labels'})

# Create datasets
train_dataset = Dataset.from_pandas(train_df)
val_dataset = Dataset.from_pandas(val_df)

# Apply tokenization
def preprocess_func(examples):
    return tokenizer(
        examples['comment'],
        padding='max_length',
        truncation=True,
        max_length=128
    )

train_dataset = train_dataset.map(preprocess_func, batched=True)
val_dataset = val_dataset.map(preprocess_func, batched=True)

# Set format for PyTorch - Fix NumPy compatibility issue
import torch

# Remove unnecessary columns and set format
train_dataset = train_dataset.remove_columns(['__index_level_0__'] if '__index_level_0__' in train_dataset.column_names else [])
val_dataset = val_dataset.remove_columns(['__index_level_0__'] if '__index_level_0__' in val_dataset.column_names else [])

# Convert labels to proper format
def convert_labels(example):
    example['labels'] = int(example['labels'])
    return example

train_dataset = train_dataset.map(convert_labels)
val_dataset = val_dataset.map(convert_labels)

# Alternative approach - create custom data collator to avoid NumPy issues
from transformers import DataCollatorWithPadding

# Don't set format, let the data collator handle it
data_collator = DataCollatorWithPadding(tokenizer=tokenizer)

print(f"Train dataset size: {len(train_dataset)}")
print(f"Validation dataset size: {len(val_dataset)}")
print(f"Train dataset columns: {train_dataset.column_names}")
print(f"Sample from train dataset: {train_dataset[0]}")

# Create Trainer with Fixed Datasets
trainer = Trainer(
    model=model,
    args=training_args,
    train_dataset=train_dataset,
    eval_dataset=val_dataset,
    compute_metrics=compute_metrics,
    callbacks=[early_stopping, overfitting_monitor]
)

# Fix for NumPy compatibility issue
import numpy as np
import warnings

# Suppress the specific NumPy warning
warnings.filterwarnings('ignore', message='Unable to avoid copy while creating an array')

# Alternative: Downgrade numpy if needed (uncomment if the above doesn't work)
# !pip install numpy==1.24.3

print(f"NumPy version: {np.__version__}")
print("NumPy compatibility fix applied")

Training
trainer.train()

# 8. Save Trained Model
trainer.save_model('./indobert-judol-classifier')
tokenizer.save_pretrained('./indobert-judol-classifier')

!zip -r indobert-judul-classifier.zip indobert-judol-classifier

from google.colab import files
files.download('indobert-judul-classifier.zip')